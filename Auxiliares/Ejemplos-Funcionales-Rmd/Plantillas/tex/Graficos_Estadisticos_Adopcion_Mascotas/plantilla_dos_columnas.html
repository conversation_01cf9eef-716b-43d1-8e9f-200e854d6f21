<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>I. E. Pedacito de Cielo - Examen Matemáticas</title>
    <style>
        @page {
            size: legal; /* 8.5" x 14" */
            margin: 0.3in 0.2in 0.5in 0.2in; /* top right bottom left */
        }
        
        body {
            font-family: 'Times New Roman', serif;
            font-size: 11pt;
            line-height: 1.2;
            margin: 0;
            padding: 0;
            background: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 15px;
            border-bottom: 1px solid #000;
            padding-bottom: 10px;
        }
        
        .header h1 {
            font-size: 14pt;
            font-weight: bold;
            margin: 0;
        }
        
        .header h2 {
            font-size: 12pt;
            font-weight: bold;
            margin: 5px 0;
        }
        
        .two-columns {
            column-count: 2;
            column-gap: 15px;
            column-rule: 1px solid #ccc;
            text-align: justify;
            break-inside: avoid;
        }
        
        .exercise {
            break-inside: avoid;
            margin-bottom: 15px;
            padding: 5px;
        }
        
        .exercise-number {
            font-weight: bold;
            font-size: 12pt;
            margin-bottom: 5px;
        }
        
        .scenario {
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .question-text {
            margin-bottom: 10px;
            text-align: justify;
        }
        
        .options {
            margin-left: 10px;
        }
        
        .option {
            margin-bottom: 3px;
        }
        
        .solution {
            font-weight: bold;
            margin-top: 10px;
            margin-bottom: 5px;
        }
        
        .solution-text {
            margin-bottom: 8px;
            font-style: italic;
        }
        
        img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 10px auto;
            break-inside: avoid;
        }
        
        /* Optimización para gráficos en columnas */
        .graph-container {
            width: 100%;
            text-align: center;
            margin: 10px 0;
            break-inside: avoid;
        }
        
        .graph-container img {
            max-width: 95%;
            height: auto;
        }
        
        /* Evitar cortes de página en elementos importantes */
        .exercise, .graph-container, .scenario, .solution {
            page-break-inside: avoid;
        }
        
        /* Footer */
        .footer {
            position: fixed;
            bottom: 0;
            width: 100%;
            text-align: center;
            font-size: 9pt;
            border-top: 1px solid #000;
            padding-top: 5px;
        }
        
        @media print {
            .two-columns {
                column-count: 2;
                column-gap: 15px;
                column-rule: 1px solid #ccc;
            }
            
            body {
                font-size: 10pt;
            }
            
            .exercise {
                break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>I. E. Pedacito de Cielo, La Tebaida, Quindío</h1>
        <h2>Simulacro Matemáticas Saber ICFES</h2>
        <p><strong>Probabilidad y Estadística - Formato Legal</strong></p>
    </div>
    
    <div class="two-columns">
        <div class="exercise">
            <div class="exercise-number">1.</div>
            <div class="scenario">Escenario</div>
            <div class="question-text">##Question##</div>
            <div class="options">
                <div class="option">##Questionlist##</div>
            </div>
            <div class="solution">Retroalimentación</div>
            <div class="solution-text">##Solution##</div>
            <div class="options">
                <div class="option">##Solutionlist##</div>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p>I. E. Pedacito de Cielo - Saber ICFES Matemáticas</p>
    </div>
</body>
</html>
